name: "Help Wanted"
description: |
  Please provide as much details to help address the issue more efficiently, including input, output, logs and screenshots.
labels:
  - help wanted
body:
  - type: checkboxes
    attributes:
      label: Checks
      description: "To ensure timely help, please confirm the following:"
      options:
        - label: This template is only for usage issues encountered.
          required: true
        - label: I have thoroughly reviewed the project documentation but couldn't find information to solve my problem.
          required: true
        - label: I have searched for existing issues, including closed ones, and couldn't find a solution.
          required: true
        - label: I am using English to submit this issue to facilitate community communication.
          required: true
  - type: textarea
    attributes:
      label: Environment Details
      description: "Provide details such as OS, Python version, and any relevant software or dependencies."
      placeholder: |
        e.g., macOS 13.5, Python 3.10, torch==2.3.0, Gradio 4.44.1
        If training or finetuning related, provide detailed configuration including GPU info and training setup.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: |
        Include detailed steps, screenshots, and logs. Provide used prompt wav and text. Use the correct markdown syntax for code blocks.
      placeholder: |
        1. Create a new conda environment.
        2. Clone the repository and install as pip package.
        3. Run the command: `f5-tts_infer-gradio` with no ref_text provided.
        4. Stuck there with the following message... (attach logs and also error msg e.g. after ctrl-c).
        5. Prompt & generated wavs are [change suffix to .mp4 to enable direct upload or pack all to .zip].
        6. Reference audio's transcription or provided ref_text is `xxx`, and text to generate is `xxx`.
    validations:
      required: true
  - type: textarea
    attributes:
      label: ✔️ Expected Behavior
      placeholder: Describe what you expected to happen in detail, e.g. output a generated audio.
    validations:
      required: false
  - type: textarea
    attributes:
      label: ❌ Actual Behavior
      placeholder: Describe what actually happened in detail, failure messages, etc.
    validations:
      required: false