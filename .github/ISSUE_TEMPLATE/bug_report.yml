name: "Bug Report"
description: |
  Please provide as much details to help address the issue more efficiently, including input, output, logs and screenshots.
labels:
  - bug
body:
  - type: checkboxes
    attributes:
      label: Checks
      description: "To ensure timely help, please confirm the following:"
      options:
        - label: This template is only for bug reports, usage problems go with 'Help Wanted'.
          required: true
        - label: I have thoroughly reviewed the project documentation but couldn't find information to solve my problem.
          required: true
        - label: I have searched for existing issues, including closed ones, and couldn't find a solution.
          required: true
        - label: I am using English to submit this issue to facilitate community communication.
          required: true
  - type: textarea
    attributes:
      label: Environment Details
      description: "Provide details including OS, GPU info, Python version, any relevant software or dependencies, and trainer setting."
      placeholder: e.g., CentOS Linux 7, 4 * RTX 3090, Python 3.10, torch==2.3.0+cu118, cuda 11.8, config yaml is ...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: |
        Include detailed steps, screenshots, and logs. Use the correct markdown syntax for code blocks.
      placeholder: |
        1. Create a new conda environment.
        2. Clone the repository, install as local editable and properly set up.
        3. Run the command: `accelerate launch src/f5_tts/train/train.py`.
        4. Have following error message... (attach logs).
    validations:
      required: true
  - type: textarea
    attributes:
      label: ✔️ Expected Behavior
      placeholder: Describe in detail what you expected to happen.
    validations:
      required: false
  - type: textarea
    attributes:
      label: ❌ Actual Behavior
      placeholder: Describe in detail what actually happened.
    validations:
      required: false