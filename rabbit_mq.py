import asyncio
from typing import Callable, Dict, Optional
import aio_pika
import logging

logger = logging.getLogger(__name__)

class RabbitMQLoadBalancer:
    def __init__(
        self,
        amqp_url: str,
        queue_name: str,
        max_capacity: int = 20,
        prefetch_count: int = 20,
        connection_timeout: float = 30.0,
        processor_timeout: Optional[float] = 60.0,  # seconds
        max_queue_length: int = 50,  # Default queue length limit
        on_success: Optional[Callable] = None,
        on_failure: Optional[Callable] = None
    ):
        self.amqp_url = amqp_url
        self.queue_name = queue_name
        self.max_capacity = max_capacity
        self.prefetch_count = prefetch_count
        self.connection_timeout = connection_timeout
        self.processor_timeout = processor_timeout
        self.max_queue_length = max_queue_length
        self.on_success = on_success
        self.on_failure = on_failure

        self.current_capacity = 0
        self._capacity_lock = asyncio.Lock()

        self.connection = None
        self.channel = None
        self.queue = None

        self.active_jobs: Dict[str, asyncio.Task] = {}
        self._consumer_tag = None
        self._shutdown_event = asyncio.Event()
        self._reconnect_lock = asyncio.Lock()

        self._consumer_processor = None

    async def connect(self) -> None:
        try:
            self.connection = await aio_pika.connect_robust(
                self.amqp_url,
                timeout=self.connection_timeout
            )
            self.channel = await self.connection.channel()
            await self.channel.set_qos(prefetch_count=self.prefetch_count)

            # Declare queue with max length and overflow policy
            queue_args = {
                "x-max-length": self.max_queue_length,
                "x-overflow": "reject-publish"
            }

            self.queue = await self.channel.declare_queue(
                self.queue_name,
                durable=True,
                auto_delete=False,
                arguments=queue_args
            )
            logger.info(f"Connected to RabbitMQ and declared queue: {self.queue_name}")
        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ: {str(e)}")
            await self.close()
            raise

    async def close(self) -> None:
        try:
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
                logger.info("RabbitMQ connection closed")
        except Exception as e:
            logger.error(f"Error closing RabbitMQ connection: {str(e)}")

        self.connection = None
        self.channel = None
        self.queue = None

    async def shutdown(self):
        logger.info("Shutting down consumer...")
        self._shutdown_event.set()
        for job_id, task in self.active_jobs.items():
            task.cancel()
            logger.info(f"Cancelled job: {job_id}")
        await self.close()

    async def process_job(self, job_id: str, message: aio_pika.IncomingMessage) -> None:
        """Process the job message and invoke callbacks."""
        try:
            if self.processor_timeout:
                # Here, you can add a timeout for job processing
                await asyncio.wait_for(self._process_message(message), timeout=self.processor_timeout)
            else:
                await self._process_message(message)
            if self.on_success:
                self.on_success(job_id)
        except asyncio.TimeoutError:
            logger.error(f"Job {job_id} timed out")
            if self.on_failure:
                self.on_failure(job_id, "timeout")
        except Exception as e:
            logger.error(f"Error processing job {job_id}: {str(e)}")
            if self.on_failure:
                self.on_failure(job_id, str(e))
        finally:
            await self._release_capacity(job_id)

    async def _process_message(self, message: aio_pika.IncomingMessage):
        """Custom logic to process the message."""
        
        await asyncio.sleep(2)  # This is where the actual work is done.

    async def _release_capacity(self, job_id: str):
        """Release capacity after job completion."""
        async with self._capacity_lock:
            self.current_capacity -= 1
            logger.info(f"Released capacity for job {job_id}. Current capacity: {self.current_capacity}")
            self.active_jobs.pop(job_id)

    async def consume(self):
        """Start consuming messages from the queue."""
        try:
            async for message in self.queue:
                job_id = message.message_id
                self.active_jobs[job_id] = asyncio.create_task(self.process_job(job_id, message))
                logger.info(f"Started job {job_id}. Active jobs: {len(self.active_jobs)}")
                await self._acquire_capacity()
        except asyncio.CancelledError:
            logger.info("Consumer was cancelled")
        except Exception as e:
            logger.error(f"Error while consuming messages: {str(e)}")
            await self.reconnect()

    async def _acquire_capacity(self):
        """Wait until capacity is available to process new jobs."""
        async with self._capacity_lock:
            while self.current_capacity >= self.max_capacity:
                logger.info("Max capacity reached. Waiting for capacity to free up...")
                await asyncio.sleep(1)  # Wait for capacity to be released
            self.current_capacity += 1
            logger.info(f"Acquired capacity. Current capacity: {self.current_capacity}")
