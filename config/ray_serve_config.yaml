# Ray Serve Configuration for F5-TTS API
# This file contains configuration settings for the Ray Serve deployment

# Ray Cluster Configuration
ray_cluster:
  # Address of existing Ray cluster (connect to existing cluster)
  address: "127.0.0.1:10001"  # Connect to existing Ray cluster

  # Resource allocation for local cluster (not used when connecting to existing)
  num_cpus: null  # Auto-detect if null
  num_gpus: null  # Auto-detect if null

  # Ray dashboard configuration
  dashboard_host: "127.0.0.1"
  dashboard_port: 8265

# Ray Serve Configuration
ray_serve:
  # HTTP server configuration
  http_host: "0.0.0.0"
  http_port: 8000

  # Deployment configuration
  detached: true
  route_prefix: "/f5tts"

  # Health check configuration
  health_check_timeout: 300  # seconds

  # Deployment name and version
  deployment_name: "f5tts-service"
  deployment_version: "v1.0"

# F5-TTS Service Configuration
f5tts_service:
  # Model configuration
  default_model: "F5-TTS_v1"
  
  # GPU configuration
  gpu_device: "cuda:0"
  
  # Resource allocation per replica
  num_gpus_per_replica: 1
  num_cpus_per_replica: 5
  
  # Scaling configuration
  min_replicas: 1
  max_replicas: 3
  target_requests_per_replica: 2
  max_concurrent_queries: 10
  
  # Performance optimization
  enable_caching: true
  cache_max_size: 100
  cache_ttl: 3600  # seconds (1 hour)
  
  # Batching configuration
  batch_size: 4
  batch_timeout: 0.1  # seconds
  
  # Thread pool configuration
  max_workers: 4
  
  # Model warmup
  enable_warmup: true
  warmup_timeout: 120  # seconds

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  
  # Log file configuration (optional)
  log_file: null  # Set to file path to enable file logging
  max_log_size: "100MB"
  backup_count: 5

# Monitoring Configuration
monitoring:
  # Metrics collection
  enable_metrics: true
  metrics_port: 8080
  
  # Health check configuration
  health_check_interval: 10  # seconds
  health_check_timeout: 30   # seconds
  
  # Performance monitoring
  track_request_latency: true
  track_gpu_utilization: true
  track_memory_usage: true

# Security Configuration (optional)
security:
  # API key authentication (if needed)
  enable_auth: false
  api_key: null
  
  # CORS configuration
  enable_cors: true
  cors_origins: ["*"]
  cors_methods: ["GET", "POST"]
  cors_headers: ["*"]

# Development Configuration
development:
  # Debug mode
  debug: false
  
  # Auto-reload on code changes
  auto_reload: false
  
  # Verbose logging
  verbose: false
