# Copyright (c) 2025, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: "f5_tts"
backend: "python"
max_batch_size: 4
dynamic_batching {
    max_queue_delay_microseconds: 1000
}
parameters [
  {
    key: "vocab_file"
    value: { string_value: "${vocab}"}
  },
  {
   key: "model_path", 
   value: {string_value:"${model}"}
  },
  {
   key: "tllm_model_dir", 
   value: {string_value:"${trtllm}"}
  },
  {
   key: "reference_audio_sample_rate", 
   value: {string_value:"16000"}
  },
  {
   key: "vocoder", 
   value: {string_value:"${vocoder}"}
  }
]

input [
  {
    name: "reference_wav"
    data_type: TYPE_FP32
    dims: [-1]
    optional: True
  },
  {
    name: "reference_wav_len"
    data_type: TYPE_INT32
    dims: [1]
    optional: True
  },
  {
    name: "reference_text"
    data_type: TYPE_STRING
    dims: [1]
  },
  {
    name: "target_text"
    data_type: TYPE_STRING
    dims: [1]
  }
]
output [
  {
    name: "waveform"
    data_type: TYPE_FP32
    dims: [ -1 ]
  }
]

instance_group [
  {
    count: 1
    kind: KIND_GPU
  }
]