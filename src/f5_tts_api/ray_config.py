"""
Configuration settings for F5-TTS Ray deployment

This module contains configuration constants that can be easily adjusted
to match your system's resource requirements.
"""

# Ray Deployment Configuration
RAY_DEPLOYMENT_CONFIG = {
    # Number of replicas (instances) to deploy
    "num_replicas": 5,
    
    # GPU allocation per replica (in GB)
    # 0.3 GB per replica × 5 replicas = 1.5 GB total GPU memory
    "gpu_per_replica": 0.3,
    
    # CPU cores per replica
    # Options for 16-24 total CPUs:
    # - 3 CPUs × 5 replicas = 15 CPUs (slightly under 16)
    # - 4 CPUs × 5 replicas = 20 CPUs (within range)
    # - 5 CPUs × 5 replicas = 25 CPUs (slightly over 24)
    "cpu_per_replica": 4,  # Recommended: 4 CPUs per replica = 20 total

    # Maximum concurrent queries the deployment can handle
    "max_concurrent_queries": 10,
    
    # GPU device to use (cuda:0 as specified)
    "gpu_device": "cuda:0",
    
    # Model configuration
    "model_type": "F5-TTS_v1",
    
    # Service name and routing
    "service_name": "f5tts-service",
    "route_prefix": "/f5tts",
}

# Ray Cluster Configuration
RAY_CLUSTER_CONFIG = {
    # Total cluster resources (should be >= deployment requirements)
    "total_cpus": 24,      # Should be >= num_replicas × cpu_per_replica
    "total_gpus": 2,       # Should be >= total GPU memory needed
    
    # Ray dashboard configuration
    "dashboard_host": "0.0.0.0",
    "dashboard_port": 8265,
    
    # Ray Serve HTTP configuration
    "serve_host": "0.0.0.0",
    "serve_port": 8001,
}

# Validation function
def validate_config():
    """Validate that the configuration is consistent."""
    deployment = RAY_DEPLOYMENT_CONFIG
    cluster = RAY_CLUSTER_CONFIG
    
    # Calculate total resource requirements
    total_cpu_needed = deployment["num_replicas"] * deployment["cpu_per_replica"]
    total_gpu_needed = deployment["num_replicas"] * deployment["gpu_per_replica"]
    
    warnings = []
    
    # Check CPU allocation
    if total_cpu_needed > cluster["total_cpus"]:
        warnings.append(
            f"CPU requirement ({total_cpu_needed}) exceeds cluster capacity ({cluster['total_cpus']})"
        )
    
    # Check GPU allocation
    if total_gpu_needed > cluster["total_gpus"]:
        warnings.append(
            f"GPU requirement ({total_gpu_needed:.1f} GB) exceeds cluster capacity ({cluster['total_gpus']} GPUs)"
        )
    
    # Check if CPU allocation is in the requested range (16-24)
    if not (16 <= total_cpu_needed <= 24):
        warnings.append(
            f"Total CPU allocation ({total_cpu_needed}) is outside requested range (16-24)"
        )
    
    return warnings


def get_deployment_config():
    """Get the deployment configuration with validation."""
    warnings = validate_config()
    if warnings:
        print("Configuration warnings:")
        for warning in warnings:
            print(f"  - {warning}")
    
    return RAY_DEPLOYMENT_CONFIG


def get_cluster_config():
    """Get the cluster configuration."""
    return RAY_CLUSTER_CONFIG


if __name__ == "__main__":
    # Print current configuration
    print("F5-TTS Ray Deployment Configuration")
    print("=" * 40)
    
    deployment = RAY_DEPLOYMENT_CONFIG
    cluster = RAY_CLUSTER_CONFIG
    
    print(f"Replicas: {deployment['num_replicas']}")
    print(f"GPU per replica: {deployment['gpu_per_replica']} GB")
    print(f"CPU per replica: {deployment['cpu_per_replica']}")
    print(f"Total GPU needed: {deployment['num_replicas'] * deployment['gpu_per_replica']:.1f} GB")
    print(f"Total CPU needed: {deployment['num_replicas'] * deployment['cpu_per_replica']}")
    print(f"Max concurrent queries: {deployment['max_concurrent_queries']}")
    print()
    
    # Validate configuration
    warnings = validate_config()
    if warnings:
        print("Warnings:")
        for warning in warnings:
            print(f"  - {warning}")
    else:
        print("✓ Configuration is valid")
