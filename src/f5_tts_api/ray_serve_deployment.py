"""
Ray Serve deployment for F5-TTS API

This module provides a Ray Serve deployment wrapper around the existing F5TTSWrapper
to enable distributed inference with GPU resource management.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, Tuple
import tempfile
import os

import ray
from ray import serve
import torch
import numpy as np

from .f5tts_wrapper import F5TTSWrapper
from .ray_config import get_deployment_config


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Load configuration
config = get_deployment_config()
NUM_REPLICAS = config["num_replicas"]
GPU_PER_REPLICA = config["gpu_per_replica"]
CPU_PER_REPLICA = config["cpu_per_replica"]
MAX_CONCURRENT_QUERIES = config["max_concurrent_queries"]
GPU_DEVICE = config["gpu_device"]
MODEL_TYPE = config["model_type"]


@serve.deployment(
    name="f5tts-service",
    num_replicas=NUM_REPLICAS,
    ray_actor_options={
        "num_gpus": GPU_PER_REPLICA,  # GPU memory per replica
        "num_cpus": CPU_PER_REPLICA,  # CPU cores per replica
        "resources": {"cuda:0": GPU_PER_REPLICA}  # Specific GPU allocation to cuda:0
    },
    max_ongoing_request=MAX_CONCURRENT_QUERIES,
    autoscaling_config=None,  # Disable autoscaling for fixed resource allocation
)
class F5TTSServeDeployment:
    """
    Ray Serve deployment for F5-TTS inference.
    
    This class wraps the existing F5TTSWrapper to provide distributed inference
    capabilities with proper GPU resource management.
    """
    
    def __init__(self):
        """Initialize the F5-TTS service deployment."""
        logger.info("Initializing F5TTS Ray Serve deployment...")
        
        # Initialize the F5TTS wrapper with configured device and model
        self.f5tts_wrapper = F5TTSWrapper(
            model_type=MODEL_TYPE,
            device=GPU_DEVICE,
            hf_cache_dir=None
        )
        
        # Track initialization status
        self._initialized = True
        self._start_time = time.time()
        
        logger.info("F5TTS Ray Serve deployment initialized successfully")
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Health check endpoint for the service.
        
        Returns:
            Dict containing health status and system information
        """
        try:
            uptime = time.time() - self._start_time
            
            # Check GPU availability
            gpu_available = torch.cuda.is_available()
            gpu_device = torch.cuda.current_device() if gpu_available else None
            gpu_memory = torch.cuda.get_device_properties(gpu_device).total_memory if gpu_available else None
            
            return {
                "status": "healthy",
                "uptime_seconds": uptime,
                "initialized": self._initialized,
                "gpu_available": gpu_available,
                "gpu_device": gpu_device,
                "gpu_memory_gb": gpu_memory / (1024**3) if gpu_memory else None,
                "model_type": self.f5tts_wrapper.model_type,
                "device": str(self.f5tts_wrapper.device)
            }
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def get_system_info(self) -> Dict[str, Any]:
        """
        Get detailed system information.
        
        Returns:
            Dict containing system and model information
        """
        try:
            # Get Ray actor info
            actor_id = ray.get_runtime_context().get_actor_id()
            
            # Get GPU info
            gpu_info = {}
            if torch.cuda.is_available():
                gpu_info = {
                    "device_count": torch.cuda.device_count(),
                    "current_device": torch.cuda.current_device(),
                    "device_name": torch.cuda.get_device_name(),
                    "memory_allocated": torch.cuda.memory_allocated(),
                    "memory_reserved": torch.cuda.memory_reserved(),
                    "memory_total": torch.cuda.get_device_properties(0).total_memory
                }
            
            return {
                "actor_id": actor_id,
                "model_type": self.f5tts_wrapper.model_type,
                "device": str(self.f5tts_wrapper.device),
                "gpu_info": gpu_info,
                "num_actors": 1,  # This actor
                "uptime": time.time() - self._start_time
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {str(e)}")
            return {"error": str(e)}
    
    async def synthesize(
        self,
        ref_audio_path: str,
        ref_text: str,
        gen_text: str,
        model_type: Optional[str] = None,
        remove_silence: bool = False,
        seed: int = -1,
        cross_fade_duration: float = 0.15,
        nfe_step: int = 32,        "num_cpus": 0.5,  # Half CPU per replica to reduce resource pressure
        
        speed: float = 1.0
    ) -> Dict[str, Any]:
        """
        Synthesize speech using F5-TTS.
        
        Args:
            ref_audio_path: Path to reference audio file
            ref_text: Reference text (if empty, will be transcribed)
            gen_text: Text to generate
            model_type: Model type to use (optional)
            remove_silence: Whether to remove silence from output
            seed: Random seed (-1 for random)
            cross_fade_duration: Cross-fade duration between segments
            nfe_step: Number of denoising steps
            speed: Speed multiplier
            
        Returns:
            Dict containing synthesis results
        """
        try:
            logger.info(f"Starting synthesis for text: {gen_text[:50]}...")
            start_time = time.time()
            
            # Run inference using the wrapper
            (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = self.f5tts_wrapper.infer(
                ref_audio_orig=ref_audio_path,
                ref_text=ref_text,
                gen_text=gen_text,
                model_type=model_type,
                remove_silence=remove_silence,
                seed=seed,
                cross_fade_duration=cross_fade_duration,
                nfe_step=nfe_step,
                speed=speed,
                show_info=logger.info
            )
            
            inference_time = time.time() - start_time
            
            # Convert audio data to list for JSON serialization
            if isinstance(audio_data, np.ndarray):
                audio_data = audio_data.tolist()
            
            logger.info(f"Synthesis completed in {inference_time:.2f} seconds")
            
            return {
                "success": True,
                "audio_data": audio_data,
                "sample_rate": sample_rate,
                "spectrogram_path": spectrogram_path,
                "ref_text": processed_ref_text,
                "seed": used_seed,
                "inference_time": inference_time,
                "model_type": model_type or self.f5tts_wrapper.model_type
            }
            
        except Exception as e:
            logger.error(f"Synthesis failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def __call__(self, request) -> Dict[str, Any]:
        """
        Main entry point for HTTP requests.
        
        Args:
            request: HTTP request object
            
        Returns:
            Response dictionary
        """
        try:
            # Handle different request types
            if hasattr(request, 'json'):
                data = await request.json()
            else:
                data = request
            
            # Route to appropriate method
            method = data.get("method", "synthesize")
            
            if method == "health_check":
                return await self.health_check()
            elif method == "get_system_info":
                return await self.get_system_info()
            elif method == "synthesize":
                return await self.synthesize(**{k: v for k, v in data.items() if k != "method"})
            else:
                return {
                    "success": False,
                    "error": f"Unknown method: {method}"
                }
                
        except Exception as e:
            logger.error(f"Request handling failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


# Deployment binding for Ray Serve
deployment = F5TTSServeDeployment.bind()
