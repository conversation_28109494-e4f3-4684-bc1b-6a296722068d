#!/usr/bin/env python3
"""
Test script for F5-TTS Ray deployment

This script tests the Ray service deployment to ensure it works correctly
with the existing F5TTSWrapper functionality.
"""

import sys
import time
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import ray
from ray import serve

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_deployment():
    """Test the F5TTS Ray deployment"""
    try:
        # Initialize Ray (connect to existing cluster or start new one)
        if not ray.is_initialized():
            logger.info("Initializing Ray...")
            ray.init(
                num_cpus=8,
                num_gpus=1,
                dashboard_host="0.0.0.0",
                dashboard_port=8265
            )
        
        # Start Ray Serve
        logger.info("Starting Ray Serve...")
        serve.start(
            detached=False,
            http_options={
                "host": "0.0.0.0",
                "port": 8001
            }
        )
        
        # Import and deploy the service
        logger.info("Deploying F5TTS service...")
        from f5_tts_api.ray_serve_deployment import F5TTSServeDeployment
        
        deployment = F5TTSServeDeployment.bind()
        serve.run(deployment, name="f5tts-service", route_prefix="/f5tts")
        
        # Wait for deployment to be ready
        logger.info("Waiting for deployment to be ready...")
        time.sleep(10)
        
        # Test health check
        logger.info("Testing health check...")
        deployment_handle = serve.get_deployment("f5tts-service")
        handle = deployment_handle.get_handle()
        
        health_result = ray.get(handle.health_check.remote())
        logger.info(f"Health check result: {health_result}")
        
        # Test system info
        logger.info("Testing system info...")
        system_info = ray.get(handle.get_system_info.remote())
        logger.info(f"System info: {system_info}")
        
        logger.info("✓ All tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False
    finally:
        # Cleanup
        try:
            serve.shutdown()
            ray.shutdown()
        except:
            pass


if __name__ == "__main__":
    success = test_deployment()
    sys.exit(0 if success else 1)
