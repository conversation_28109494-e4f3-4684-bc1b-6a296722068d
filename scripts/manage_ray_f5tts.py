#!/usr/bin/env python3
"""
Management script for Ray-scaled F5-TTS system
"""
import sys
import time
import logging
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import ray
from ray import serve

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def status():
    """Check status of Ray and F5TTS services"""
    logger.info("Checking Ray-scaled F5-TTS system status...")
    
    # Check Ray
    try:
        if ray.is_initialized():
            logger.info("✓ Ray cluster is running")
            
            # Get cluster resources
            resources = ray.cluster_resources()
            logger.info(f"  - CPUs: {resources.get('CPU', 0)}")
            logger.info(f"  - GPUs: {resources.get('GPU', 0)}")
            
        else:
            logger.warning("✗ Ray cluster is not running")
            return False
    except Exception as e:
        logger.error(f"✗ Error checking Ray: {str(e)}")
        return False
    
    # Check Ray Serve
    try:
        deployments = serve.list_deployments()
        if "f5tts-service" in deployments:
            logger.info("✓ F5TTS service is deployed")
            
            # Get deployment info
            deployment = serve.get_deployment("f5tts-service")
            handle = deployment.get_handle()
            
            # Test health check
            health = ray.get(handle.health_check.remote())
            logger.info(f"  - Health: {health.get('status', 'unknown')}")
            
            # Get system info
            info = ray.get(handle.get_system_info.remote())
            if "num_actors" in info:
                logger.info(f"  - Active actors: {info['num_actors']}")
                logger.info(f"  - Model type: {info.get('model_type', 'unknown')}")
            
        else:
            logger.warning("✗ F5TTS service is not deployed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error checking Ray Serve: {str(e)}")
        return False
    
    logger.info("✓ All services are running normally")
    return True


def stop():
    """Stop Ray and F5TTS services"""
    logger.info("Stopping Ray-scaled F5-TTS system...")
    
    try:
        # Stop Ray Serve
        if ray.is_initialized():
            try:
                serve.shutdown()
                logger.info("✓ Ray Serve stopped")
            except Exception as e:
                logger.warning(f"Ray Serve shutdown warning: {str(e)}")
            
            # Stop Ray
            ray.shutdown()
            logger.info("✓ Ray cluster stopped")
        else:
            logger.info("Ray was not running")
            
    except Exception as e:
        logger.error(f"Error stopping services: {str(e)}")
        return False
    
    logger.info("✓ All services stopped")
    return True


def restart():
    """Restart the F5TTS service"""
    logger.info("Restarting F5TTS service...")
    
    try:
        if not ray.is_initialized():
            logger.error("Ray cluster is not running. Use 'start' command first.")
            return False
        
        # Stop existing deployment
        try:
            serve.delete("f5tts-service")
            logger.info("Stopped existing F5TTS service")
            time.sleep(2)
        except Exception:
            pass
        
        # Redeploy
        from f5_tts_api.ray_serve_deployment import F5TTSServeDeployment
        
        F5TTSServeDeployment.deploy(
            name="f5tts-service",
            route_prefix="/f5tts"
        )
        
        logger.info("✓ F5TTS service restarted")
        return True
        
    except Exception as e:
        logger.error(f"Error restarting service: {str(e)}")
        return False


def logs():
    """Show recent logs (placeholder - would need proper log aggregation)"""
    logger.info("Recent logs:")
    logger.info("(Log aggregation not implemented - check Ray dashboard for detailed logs)")
    logger.info("Ray dashboard: http://localhost:8265")


def test():
    """Test the F5TTS service"""
    logger.info("Testing F5TTS service...")
    
    try:
        if not ray.is_initialized():
            logger.error("Ray cluster is not running")
            return False
        
        deployment = serve.get_deployment("f5tts-service")
        handle = deployment.get_handle()
        
        # Test health check
        logger.info("Testing health check...")
        health = ray.get(handle.health_check.remote())
        logger.info(f"Health check result: {health}")
        
        # Test system info
        logger.info("Testing system info...")
        info = ray.get(handle.get_system_info.remote())
        logger.info(f"System info: {info}")
        
        logger.info("✓ All tests passed")
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Manage Ray-scaled F5-TTS system")
    parser.add_argument(
        "command",
        choices=["status", "stop", "restart", "logs", "test"],
        help="Command to execute"
    )
    
    args = parser.parse_args()
    
    # Connect to existing Ray cluster if needed
    if args.command in ["status", "restart", "test"]:
        try:
            if not ray.is_initialized():
                ray.init(address="auto")
        except Exception as e:
            logger.error(f"Failed to connect to Ray cluster: {str(e)}")
            return 1
    
    # Execute command
    if args.command == "status":
        success = status()
    elif args.command == "stop":
        success = stop()
    elif args.command == "restart":
        success = restart()
    elif args.command == "logs":
        logs()
        success = True
    elif args.command == "test":
        success = test()
    else:
        logger.error(f"Unknown command: {args.command}")
        return 1
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
