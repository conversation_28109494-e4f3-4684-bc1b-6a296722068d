#!/usr/bin/env python3
"""
Startup script for Ray-scaled F5-TTS system
Creates 5 instances with 0.3 GPU and 1.2 CPU cores each
"""
import os
import sys
import time
import logging
import argparse
import subprocess
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import ray
from ray import serve

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def check_gpu_availability():
    """Check if CUDA GPU 0 is available"""
    try:
        import torch
        if not torch.cuda.is_available():
            logger.error("CUDA is not available")
            return False
        
        if torch.cuda.device_count() < 1:
            logger.error("No CUDA devices found")
            return False
        
        # Test GPU 0 specifically
        torch.cuda.set_device(0)
        logger.info(f"GPU 0 available: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU 0 memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        return True
        
    except Exception as e:
        logger.error(f"Error checking GPU: {str(e)}")
        return False


def start_ray_cluster(num_cpus=8, num_gpus=2):
    """Start Ray cluster with specified resources or connect to existing"""
    try:
        if ray.is_initialized():
            logger.info("Ray is already initialized")
            return True

        # Try to connect to existing Ray cluster first
        try:
            ray.init(address="auto")
            logger.info("Connected to existing Ray cluster")
            logger.info(f"Ray dashboard available at: http://localhost:8265")
            return True
        except Exception:
            logger.info("No existing Ray cluster found, starting new one...")

        logger.info(f"Starting Ray cluster with {num_cpus} CPUs and {num_gpus} GPUs")
        ray.init(
            num_cpus=num_cpus,
            num_gpus=num_gpus,
            dashboard_host="0.0.0.0",
            dashboard_port=8266,  # Use different port to avoid conflicts
            include_dashboard=True
        )

        logger.info("Ray cluster started successfully")
        logger.info(f"Ray dashboard available at: http://localhost:8266")
        return True

    except Exception as e:
        logger.error(f"Failed to start Ray cluster: {str(e)}")
        return False


def deploy_f5tts_service():
    """Deploy the F5TTS service with Ray Serve"""
    try:
        from f5_tts_api.ray_serve_deployment import F5TTSServeDeployment
        
        # Start Ray Serve
        serve.start(
            detached=True,
            http_options={
                "host": "0.0.0.0",
                "port": 8001
            }
        )
        
        # Deploy the F5TTS service
        deployment = F5TTSServeDeployment.bind()
        serve.run(deployment, name="f5tts-service", route_prefix="/f5tts")
        
        logger.info("F5TTS service deployed successfully")
        logger.info("Service available at: http://0.0.0.0:8001/f5tts")
        return True
        
    except Exception as e:
        logger.error(f"Failed to deploy F5TTS service: {str(e)}")
        return False


def wait_for_deployment_ready(timeout=120):
    """Wait for deployment to be ready"""
    logger.info("Waiting for deployment to be ready...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            deployment = serve.get_deployment("f5tts-service")
            handle = deployment.get_handle()
            
            # Test health check
            health = ray.get(handle.health_check.remote())
            if health.get("status") == "healthy":
                logger.info("Deployment is ready and healthy")
                return True
                
        except Exception as e:
            logger.debug(f"Deployment not ready yet: {str(e)}")
        
        time.sleep(5)
    
    logger.error(f"Deployment not ready after {timeout} seconds")
    return False


def start_fastapi_server():
    """Start the FastAPI server"""
    try:
        logger.info("Starting FastAPI server...")
        
        # Change to project root directory
        project_root = Path(__file__).parent.parent
        os.chdir(project_root)
        
        # Start uvicorn server
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8080",  # Use different port from Ray Serve
            "--reload"
        ]
        
        process = subprocess.Popen(cmd)
        logger.info("FastAPI server started on port 8080")
        logger.info("API available at: http://localhost:8080")
        
        return process
        
    except Exception as e:
        logger.error(f"Failed to start FastAPI server: {str(e)}")
        return None


def main():
    parser = argparse.ArgumentParser(description="Start Ray-scaled F5-TTS system")
    parser.add_argument("--num-cpus", type=int, default=8, help="Number of CPUs for Ray cluster")
    parser.add_argument("--num-gpus", type=int, default=2, help="Number of GPUs for Ray cluster")
    parser.add_argument("--skip-gpu-check", action="store_true", help="Skip GPU availability check")
    parser.add_argument("--no-fastapi", action="store_true", help="Don't start FastAPI server")
    
    args = parser.parse_args()
    
    logger.info("Starting Ray-scaled F5-TTS system...")
    logger.info("Configuration:")
    logger.info(f"  - 5 F5TTS replicas")
    logger.info(f"  - 0.3 GPU per replica (1.5 GPU total)")
    logger.info(f"  - 1.2 CPU cores per replica (6 CPU total)")
    logger.info(f"  - Using CUDA device 0")
    logger.info(f"  - 5 concurrent requests supported")
    
    # Check GPU availability
    if not args.skip_gpu_check:
        if not check_gpu_availability():
            logger.error("GPU check failed. Use --skip-gpu-check to bypass.")
            return 1
    
    # Start Ray cluster
    if not start_ray_cluster(args.num_cpus, args.num_gpus):
        logger.error("Failed to start Ray cluster")
        return 1
    
    # Deploy F5TTS service
    if not deploy_f5tts_service():
        logger.error("Failed to deploy F5TTS service")
        return 1
    
    # Wait for deployment to be ready
    if not wait_for_deployment_ready():
        logger.error("Deployment failed to become ready")
        return 1
    
    # Start FastAPI server
    fastapi_process = None
    if not args.no_fastapi:
        fastapi_process = start_fastapi_server()
        if not fastapi_process:
            logger.error("Failed to start FastAPI server")
            return 1
    
    logger.info("=" * 60)
    logger.info("Ray-scaled F5-TTS system started successfully!")
    logger.info("=" * 60)
    logger.info("Services:")
    logger.info("  - Ray Dashboard: http://localhost:8265")
    logger.info("  - Ray Serve API: http://localhost:8001/f5tts")
    if not args.no_fastapi:
        logger.info("  - FastAPI Server: http://localhost:8080")
    logger.info("=" * 60)
    logger.info("Press Ctrl+C to stop all services")
    
    try:
        # Keep running until interrupted
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down services...")
        
        if fastapi_process:
            fastapi_process.terminate()
            fastapi_process.wait()
        
        serve.shutdown()
        ray.shutdown()
        
        logger.info("All services stopped")
        return 0


if __name__ == "__main__":
    sys.exit(main())
