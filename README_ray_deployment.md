# F5-TTS Ray Service Deployment

This document describes the Ray service deployment for F5-TTS that provides distributed inference with GPU resource management.

## Overview

The Ray service deployment wraps the existing `F5TTSWrapper` to provide:
- **Distributed inference** across multiple replicas
- **GPU resource management** with fractional GPU allocation
- **Configurable CPU allocation** (16-24 cores as requested)
- **Health monitoring** and system information endpoints
- **Scalable architecture** for high-throughput inference

## Configuration

### Resource Allocation

The deployment is configured with the following default settings:

- **Number of replicas**: 5
- **GPU allocation**: 0.3 GB per replica (1.5 GB total)
- **CPU allocation**: 4 cores per replica (20 cores total)
- **GPU device**: cuda:0
- **Max concurrent queries**: 10

### Adjusting CPU Allocation

To adjust the CPU allocation to your preferred range (16-24 cores), edit `src/f5_tts_api/ray_config.py`:

```python
RAY_DEPLOYMENT_CONFIG = {
    "cpu_per_replica": 3,  # 3 × 5 = 15 CPUs (under range)
    "cpu_per_replica": 4,  # 4 × 5 = 20 CPUs (recommended)
    "cpu_per_replica": 5,  # 5 × 5 = 25 CPUs (over range)
    # ... other settings
}
```

## Files Created

1. **`src/f5_tts_api/ray_serve_deployment.py`** - Main Ray service deployment
2. **`src/f5_tts_api/ray_config.py`** - Configuration settings
3. **`test_ray_deployment.py`** - Test script for the deployment

## Usage

### Starting the Service

The existing startup scripts should work with the new deployment:

```bash
# Using the existing startup script (if available)
python scripts/start_ray_f5tts.py

# Or manually start Ray and deploy the service
python test_ray_deployment.py
```

### API Endpoints

The service provides the following endpoints:

#### Health Check
```python
# Via Ray handle
health = ray.get(handle.health_check.remote())

# Via HTTP (if using Ray Serve HTTP)
POST /f5tts
{
    "method": "health_check"
}
```

#### System Information
```python
# Via Ray handle
info = ray.get(handle.get_system_info.remote())

# Via HTTP
POST /f5tts
{
    "method": "get_system_info"
}
```

#### Speech Synthesis
```python
# Via Ray handle
result = ray.get(handle.synthesize.remote(
    ref_audio_path="path/to/reference.wav",
    ref_text="Reference text",
    gen_text="Text to synthesize",
    model_type="F5-TTS_v1",  # optional
    remove_silence=False,    # optional
    seed=-1,                 # optional
    cross_fade_duration=0.15, # optional
    nfe_step=32,             # optional
    speed=1.0                # optional
))

# Via HTTP
POST /f5tts
{
    "method": "synthesize",
    "ref_audio_path": "path/to/reference.wav",
    "ref_text": "Reference text",
    "gen_text": "Text to synthesize",
    "model_type": "F5-TTS_v1",
    "remove_silence": false,
    "seed": -1,
    "cross_fade_duration": 0.15,
    "nfe_step": 32,
    "speed": 1.0
}
```

## Architecture

The Ray service deployment follows this architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                    Ray Cluster                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Replica 1     │  │   Replica 2     │  │   Replica N  │ │
│  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌──────────┐ │ │
│  │ │F5TTSWrapper │ │  │ │F5TTSWrapper │ │  │ │F5TTSWrapper│ │ │
│  │ │0.3 GPU      │ │  │ │0.3 GPU      │ │  │ │0.3 GPU   │ │ │
│  │ │4 CPU cores  │ │  │ │4 CPU cores  │ │  │ │4 CPU cores│ │ │
│  │ └─────────────┘ │  │ └─────────────┘ │  │ └──────────┘ │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Ray Serve     │
                    │  Load Balancer  │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   HTTP API      │
                    │  (Port 8001)    │
                    └─────────────────┘
```

## Key Features

1. **Wraps Existing Functionality**: Uses the existing `F5TTSWrapper` without modification
2. **Resource Management**: Precise GPU and CPU allocation per replica
3. **Scalability**: Easy to adjust number of replicas and resource allocation
4. **Health Monitoring**: Built-in health checks and system monitoring
5. **Configuration-Driven**: Easy to adjust settings via configuration files
6. **Ray Integration**: Leverages Ray's distributed computing capabilities

## Testing

Run the test script to verify the deployment:

```bash
python test_ray_deployment.py
```

This will:
1. Initialize Ray cluster
2. Deploy the F5-TTS service
3. Test health check endpoint
4. Test system information endpoint
5. Clean up resources

## Troubleshooting

### Resource Issues
- If you get resource allocation errors, adjust the CPU/GPU settings in `ray_config.py`
- Ensure your system has sufficient resources for the configured allocation

### Import Errors
- Make sure the `src` directory is in your Python path
- Verify all dependencies are installed (Ray, PyTorch, etc.)

### GPU Issues
- Ensure CUDA is available and accessible
- Check that `cuda:0` device exists on your system
- Verify GPU memory is sufficient for the allocation

## Configuration Validation

The configuration includes validation to ensure consistency:

```bash
# Check current configuration
python src/f5_tts_api/ray_config.py
```

This will show the current settings and any warnings about resource allocation.
